import { BaseSeeder } from '@adonisjs/lucid/seeders'
import SystemUserModel from '#models/system_user_model'
import { randomUUID } from 'node:crypto'

export default class extends BaseSeeder {
  async run() {
    // Check if superadmin already exists
    const existingSuperadmin = await SystemUserModel.query()
      .where('role', 'superadmin')
      .first()

    if (existingSuperadmin) {
      console.log('Superadmin already exists, skipping seeder')
      return
    }

    // Create the superadmin user
    const superadmin = await SystemUserModel.create({
      id: randomUUID(),
      name: 'Super Administrator',
      email: '<EMAIL>',
      password: 'SuperAdmin123!', // This will be automatically hashed
      role: 'superadmin'
    })

    console.log(`✅ Superadmin created successfully:`)
    console.log(`   Email: ${superadmin.email}`)
    console.log(`   Password: SuperAdmin123!`)
    console.log(`   Role: ${superadmin.role}`)
    console.log(`   ID: ${superadmin.id}`)

    // Optionally create additional admin users for testing
    const adminUsers = [
      {
        id: randomUUID(),
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'Admin123!',
        role: 'admin' as const
      },
      {
        id: randomUUID(),
        name: 'Support User',
        email: '<EMAIL>',
        password: 'Support123!',
        role: 'support' as const
      }
    ]

    for (const userData of adminUsers) {
      const existingUser = await SystemUserModel.query()
        .where('email', userData.email)
        .first()

      if (!existingUser) {
        const user = await SystemUserModel.create(userData)
        console.log(`✅ ${userData.role} user created: ${user.email}`)
      } else {
        console.log(`⚠️  ${userData.role} user already exists: ${userData.email}`)
      }
    }
  }
}