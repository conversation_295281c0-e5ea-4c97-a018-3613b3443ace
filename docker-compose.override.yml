version: "3.9"

services:
  app:
    build:
      context: .
      target: development
    container_name: adonis_app
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - "3333:3333"
    environment:
      NODE_ENV: development
      DB_CONNECTION: pg
      PG_HOST: postgres
      PG_PORT: 5432
      PG_USER: adonis
      PG_PASSWORD: secret
      PG_DB_NAME: adonis_db
    depends_on:
      postgres:
        condition: service_healthy  # Wait for DB to be ready
    # Optional: Add healthcheck for development too
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/health/liveness"]
      interval: 60s  # Less frequent in development
      timeout: 15s   # More timeout for dev
      retries: 3
      start_period: 40s  # Give more time to start in dev    

  postgres:
    image: postgres:15
    container_name: adonis_postgres
    restart: unless-stopped  # Changed from always
    environment:
      POSTGRES_USER: adonis
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: adonis_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U adonis -d adonis_db"]
      interval: 5s
      timeout: 5s
      retries: 10

  adminer:
    image: adminer
    container_name: adonis_adminer
    restart: unless-stopped  # Changed from always
    ports:
      - "8080:8080"
    depends_on:
      - postgres

volumes:
  postgres_data: